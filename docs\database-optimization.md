# Database Optimization Guide

This guide provides comprehensive information about optimizing MySQL database performance for the Alumni Portal backend.

## Index Recommendations

### High Priority Indexes

These indexes should be implemented immediately for optimal performance:

```sql
-- User table indexes
CREATE INDEX idx_user_email ON users (email);
CREATE INDEX idx_user_status_role ON users (status, role);
CREATE INDEX idx_user_created_at ON users (createdAt);

-- Job table indexes
CREATE INDEX idx_job_status_created ON jobs (isActive, createdAt DESC);
CREATE INDEX idx_job_company ON jobs (company);
CREATE INDEX idx_job_location ON jobs (location);
CREATE INDEX idx_job_type ON jobs (type);

-- Event table indexes
CREATE INDEX idx_event_date_status ON events (startTime, isActive);
CREATE INDEX idx_event_created_at ON events (createdAt DESC);

-- Message table indexes
CREATE INDEX idx_message_sender_created ON messages (senderId, createdAt DESC);
CREATE INDEX idx_message_receiver_created ON messages (receiverId, createdAt DESC);
CREATE INDEX idx_message_conversation ON messages (senderId, receiverId, createdAt DESC);

-- Notification table indexes
CREATE INDEX idx_notification_user_read ON notifications (userId, isRead, createdAt DESC);
CREATE INDEX idx_notification_type ON notifications (type);
```

### Medium Priority Indexes

These indexes can improve performance for specific use cases:

```sql
-- User table additional indexes
CREATE INDEX idx_user_batch ON users (batch);
CREATE INDEX idx_user_course ON users (course);

-- Job table additional indexes
CREATE INDEX idx_job_posted_by ON jobs (postedById);
CREATE INDEX idx_job_expires_at ON jobs (expiresAt);

-- Event table additional indexes
CREATE INDEX idx_event_organizer ON events (organizerId);
CREATE INDEX idx_event_location ON events (location);
```

### Composite Indexes for Complex Queries

```sql
-- For job search with multiple filters
CREATE INDEX idx_job_search ON jobs (isActive, type, location, createdAt DESC);

-- For user search and filtering
CREATE INDEX idx_user_search ON users (status, role, course, batch);

-- For message threading
CREATE INDEX idx_message_thread ON messages (senderId, receiverId, createdAt);
```

## Query Optimization Patterns

### 1. Pagination Best Practices

```typescript
// ❌ Avoid OFFSET for large datasets
const users = await prisma.user.findMany({
  skip: page * limit, // Slow for large offsets
  take: limit,
});

// ✅ Use cursor-based pagination for better performance
const users = await prisma.user.findMany({
  cursor: lastUserId ? { id: lastUserId } : undefined,
  take: limit,
  orderBy: { createdAt: 'desc' },
});
```

### 2. Efficient Filtering

```typescript
// ❌ Avoid OR conditions when possible
const jobs = await prisma.job.findMany({
  where: {
    OR: [
      { title: { contains: query } },
      { description: { contains: query } },
      { companyName: { contains: query } },
    ],
  },
});

// ✅ Use full-text search or separate queries
const jobs = await prisma.job.findMany({
  where: {
    AND: [
      { status: 'ACTIVE' },
      {
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { companyName: { contains: query, mode: 'insensitive' } },
        ],
      },
    ],
  },
});
```

### 3. Optimized Includes

```typescript
// ❌ Avoid deep nested includes
const user = await prisma.user.findUnique({
  where: { id },
  include: {
    jobs: {
      include: {
        applications: {
          include: {
            user: true,
          },
        },
      },
    },
  },
});

// ✅ Use selective includes and separate queries
const user = await prisma.user.findUnique({
  where: { id },
  include: {
    jobs: {
      select: {
        id: true,
        title: true,
        status: true,
        createdAt: true,
      },
    },
  },
});
```

## Performance Monitoring

### Query Performance Metrics

The system automatically tracks:
- Query execution time
- Result set size
- Query frequency
- Slow query identification

### Monitoring Endpoints

```
GET /api/metrics/performance - Overall performance metrics
GET /api/admin/database/health - Database health report
GET /api/admin/database/slow-queries - Slow query analysis
```

### Performance Thresholds

- **Slow Query**: > 1000ms
- **Large Result Set**: > 10,000 records
- **Warning Threshold**: > 500ms average

## Database Configuration

### Connection Pool Settings

```env
# Optimal connection pool settings
DATABASE_URL="mysql://user:pass@host:3306/db?connection_limit=20&pool_timeout=20&socket_timeout=60"

# Read replica for scaling reads
DATABASE_READ_REPLICA_URL="mysql://user:pass@read-host:3306/db?connection_limit=15"
```

### Prisma Configuration

```typescript
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}
```

## Query Optimization Checklist

### Before Deployment

- [ ] All high-priority indexes are created
- [ ] Query performance is tested with realistic data volumes
- [ ] Slow query logging is enabled
- [ ] Connection pooling is properly configured

### Regular Maintenance

- [ ] Monitor slow query reports weekly
- [ ] Review and update indexes based on usage patterns
- [ ] Analyze query performance metrics monthly
- [ ] Update statistics and vacuum database regularly

### Performance Testing

- [ ] Load test with expected data volumes
- [ ] Test pagination performance with large datasets
- [ ] Verify index usage with EXPLAIN ANALYZE
- [ ] Monitor memory usage during peak loads

## Common Anti-Patterns to Avoid

### 1. N+1 Query Problem

```typescript
// ❌ N+1 queries
const users = await prisma.user.findMany();
for (const user of users) {
  const jobs = await prisma.job.findMany({
    where: { userId: user.id },
  });
}

// ✅ Single query with include
const users = await prisma.user.findMany({
  include: {
    jobs: true,
  },
});
```

### 2. Unnecessary Data Fetching

```typescript
// ❌ Fetching all fields
const users = await prisma.user.findMany();

// ✅ Select only needed fields
const users = await prisma.user.findMany({
  select: {
    id: true,
    name: true,
    email: true,
  },
});
```

### 3. Inefficient Counting

```typescript
// ❌ Counting with findMany
const users = await prisma.user.findMany();
const count = users.length;

// ✅ Use count method
const count = await prisma.user.count();
```

## Scaling Strategies

### Read Replicas

- Use read replicas for read-heavy operations
- Route analytics queries to read replicas
- Implement read/write splitting in the application

### Caching

- Cache frequently accessed data
- Use Redis for session and temporary data
- Implement query result caching

### Partitioning

- Consider table partitioning for large tables
- Partition by date for time-series data
- Use horizontal partitioning for user data

## Monitoring and Alerting

### Key Metrics to Monitor

- Average query response time
- Number of slow queries per hour
- Database connection pool utilization
- Cache hit rates
- Database CPU and memory usage

### Alert Thresholds

- Slow queries > 10 per minute
- Average response time > 500ms
- Connection pool utilization > 80%
- Cache hit rate < 90%

## Tools and Resources

### Database Analysis Tools

- `PERFORMANCE_SCHEMA` for MySQL query analysis
- `EXPLAIN ANALYZE` for query execution plans
- MySQL Workbench for performance monitoring
- Database monitoring tools (DataDog, New Relic, etc.)

### Performance Testing

- Load testing with realistic data volumes
- Query performance benchmarking
- Connection pool stress testing

This guide should be regularly updated based on actual usage patterns and performance metrics from the production environment.

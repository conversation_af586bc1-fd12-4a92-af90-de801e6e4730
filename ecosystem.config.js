module.exports = {
  apps: [
    {
      name: "alumni-portal-api",
      script: "dist/index.js",
      instances: "max", // Use all available CPU cores
      exec_mode: "cluster",

      // Environment variables
      env: {
        NODE_ENV: "development",
        PORT: 3000,
        WS_PORT: 3001,
        CLUSTER_ENABLED: false, // Let PM2 handle clustering
        LOG_LEVEL: "debug",
      },

      env_staging: {
        NODE_ENV: "staging",
        PORT: 3000,
        WS_PORT: 3001,
        CLUSTER_ENABLED: false,
        LOG_LEVEL: "info",
      },

      env_production: {
        NODE_ENV: "production",
        PORT: 3000,
        WS_PORT: 3001,
        CLUSTER_ENABLED: false, // Let PM2 handle clustering
        LOG_LEVEL: "warn",
      },

      // Logging
      log_file: "./logs/combined.log",
      out_file: "./logs/out.log",
      error_file: "./logs/error.log",
      log_date_format: "YYYY-MM-DD HH:mm:ss Z",

      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      restart_delay: 4000,

      // Health monitoring
      min_uptime: "10s",
      max_restarts: 10,

      // Advanced PM2 features
      kill_timeout: 5000,
      listen_timeout: 3000,

      // Graceful shutdown
      shutdown_with_message: true,

      // Source map support
      source_map_support: true,

      // Merge logs from all instances
      merge_logs: true,

      // Time zone
      time: true,

      // Performance monitoring
      pmx: true,

      // Instance variables for load balancing
      instance_var: "INSTANCE_ID",

      // Auto-scaling configuration
      min_instances: 2,
      max_instances: 8,

      // Health check configuration
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
    },
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: "deploy",
      host: ["server1.example.com", "server2.example.com"],
      ref: "origin/main",
      repo: "**************:your-org/alumni-portal-backend.git",
      path: "/var/www/alumni-portal-api",
      "pre-deploy-local": "",
      "post-deploy":
        "npm install && npm run build && npm run prisma:generate && pm2 reload ecosystem.config.js --env production",
      "pre-setup": "",
      ssh_options: "ForwardAgent=yes",
    },

    staging: {
      user: "deploy",
      host: "staging.example.com",
      ref: "origin/develop",
      repo: "**************:your-org/alumni-portal-backend.git",
      path: "/var/www/alumni-portal-api-staging",
      "post-deploy":
        "npm install && npm run build && npm run prisma:generate && pm2 reload ecosystem.config.js --env staging",
      env: {
        NODE_ENV: "staging",
      },
    },
  },
};

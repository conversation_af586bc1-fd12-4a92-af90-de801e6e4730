-- MySQL Database Initialization Script for Alumni Portal
-- This script sets up the database with optimal settings for the application

-- Set character set and collation for proper Unicode support
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS alumni_portal 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE alumni_portal;

-- Create application user if it doesn't exist
CREATE USER IF NOT EXISTS 'alumni_user'@'%' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON alumni_portal.* TO 'alumni_user'@'%';

-- Set optimal MySQL settings for the application
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL innodb_log_file_size = 67108864; -- 64MB
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;

-- Enable binary logging for replication (if needed)
-- SET GLOBAL log_bin = ON;
-- SET GLOBAL server_id = 1;

-- Optimize for application workload
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL sync_binlog = 0;

-- Set timezone
SET GLOBAL time_zone = '+00:00';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Display current settings
SELECT 'Database initialization completed' AS status;
SHOW VARIABLES LIKE 'character_set_database';
SHOW VARIABLES LIKE 'collation_database';
SHOW VARIABLES LIKE 'max_connections';

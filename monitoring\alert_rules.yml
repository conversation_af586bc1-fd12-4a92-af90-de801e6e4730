groups:
  - name: alumni-portal-alerts
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: rate(http_request_total{status=~"5.."}[5m]) / rate(http_request_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (system_memory_heap_used / system_memory_heap_total) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }}"

      # Database connection issues
      - alert: DatabaseConnectionFailure
        expr: database_connection_errors > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failures"
          description: "{{ $value }} database connection failures in the last minute"

      # Redis connection issues
      - alert: RedisConnectionFailure
        expr: redis_connection_errors > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis connection failures"
          description: "{{ $value }} Redis connection failures in the last minute"

      # Queue processing delays
      - alert: QueueProcessingDelay
        expr: queue_job_waiting_time > 300
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Queue processing delays"
          description: "Jobs are waiting {{ $value }}s in the queue"

      # Low cache hit rate
      - alert: LowCacheHitRate
        expr: cache_hit_rate < 0.8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value | humanizePercentage }}"

      # Application down
      - alert: ApplicationDown
        expr: up{job="alumni-portal-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Application is down"
          description: "Alumni Portal API is not responding"

      # High CPU usage
      - alert: HighCPUUsage
        expr: system_cpu_usage > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}%"

      # Disk space low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} full"

  - name: database-alerts
    rules:
      # Slow queries
      - alert: SlowDatabaseQueries
        expr: database_slow_queries_per_minute > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High number of slow database queries"
          description: "{{ $value }} slow queries per minute"

      # Database locks
      - alert: DatabaseLocks
        expr: postgres_locks_count > 100
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High number of database locks"
          description: "{{ $value }} database locks detected"

      # Connection pool exhaustion
      - alert: ConnectionPoolExhaustion
        expr: postgres_connection_pool_usage > 0.9
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "Connection pool usage is {{ $value | humanizePercentage }}"

  - name: infrastructure-alerts
    rules:
      # Container restarts
      - alert: ContainerRestarts
        expr: increase(container_restart_count[1h]) > 5
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Container restarting frequently"
          description: "Container {{ $labels.name }} has restarted {{ $value }} times in the last hour"

      # Load balancer health
      - alert: LoadBalancerUnhealthy
        expr: nginx_upstream_server_health == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Load balancer backend unhealthy"
          description: "Nginx upstream server {{ $labels.server }} is unhealthy"

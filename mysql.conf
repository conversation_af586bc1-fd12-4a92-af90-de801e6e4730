# MySQL Configuration for Alumni Portal
# Optimized for application performance and scalability

[mysqld]
# Basic settings
user = mysql
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock

# Character set and collation
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# Connection settings
max_connections = 200
max_connect_errors = 10000
connect_timeout = 10
wait_timeout = 28800
interactive_timeout = 28800

# Buffer settings
innodb_buffer_pool_size = 512M
innodb_buffer_pool_instances = 2
innodb_log_buffer_size = 16M
key_buffer_size = 32M
sort_buffer_size = 2M
read_buffer_size = 2M
read_rnd_buffer_size = 4M
join_buffer_size = 2M

# InnoDB settings
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_log_file_size = 128M
innodb_log_files_in_group = 2
innodb_flush_method = O_DIRECT
innodb_lock_wait_timeout = 50
innodb_rollback_on_timeout = 1

# Query cache (disabled in MySQL 8.0+, but kept for compatibility)
# query_cache_type = 1
# query_cache_size = 64M
# query_cache_limit = 2M

# Logging
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# Binary logging for replication
log_bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M
sync_binlog = 1

# Performance schema
performance_schema = ON
performance_schema_max_table_instances = 400
performance_schema_max_table_handles = 4000

# Security
local_infile = 0
skip_show_database

# Timezone
default_time_zone = '+00:00'

# Table settings
table_open_cache = 2000
table_definition_cache = 1400
open_files_limit = 65535

# Thread settings
thread_cache_size = 50
thread_stack = 256K

# Temporary tables
tmp_table_size = 64M
max_heap_table_size = 64M

# MyISAM settings (for system tables)
myisam_sort_buffer_size = 8M
myisam_max_sort_file_size = 2G
myisam_repair_threads = 1

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock

[mysqldump]
quick
quote-names
max_allowed_packet = 64M
default-character-set = utf8mb4

[isamchk]
key_buffer_size = 16M
sort_buffer_size = 20M
read_buffer = 2M
write_buffer = 2M

#!/usr/bin/env node

/**
 * Auto-scaling script for Alumni Portal API
 * Monitors system metrics and scales instances based on load
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  // Scaling thresholds
  CPU_SCALE_UP_THRESHOLD: 70,    // Scale up when CPU > 70%
  CPU_SCALE_DOWN_THRESHOLD: 30,  // Scale down when CPU < 30%
  MEMORY_SCALE_UP_THRESHOLD: 80, // Scale up when memory > 80%
  RESPONSE_TIME_THRESHOLD: 1000, // Scale up when response time > 1000ms
  
  // Instance limits
  MIN_INSTANCES: 2,
  MAX_INSTANCES: 8,
  
  // Scaling behavior
  SCALE_UP_COOLDOWN: 300000,     // 5 minutes
  SCALE_DOWN_COOLDOWN: 600000,   // 10 minutes
  CHECK_INTERVAL: 60000,         // 1 minute
  
  // Metrics collection
  METRICS_WINDOW: 300000,        // 5 minutes
  
  // PM2 app name
  APP_NAME: 'alumni-portal-api',
};

class AutoScaler {
  constructor() {
    this.lastScaleAction = 0;
    this.metrics = [];
    this.isScaling = false;
  }

  // Start auto-scaling monitoring
  start() {
    console.log('🚀 Starting auto-scaler for Alumni Portal API');
    console.log(`📊 Monitoring every ${CONFIG.CHECK_INTERVAL / 1000} seconds`);
    
    // Initial metrics collection
    this.collectMetrics();
    
    // Start monitoring loop
    setInterval(() => {
      this.monitorAndScale();
    }, CONFIG.CHECK_INTERVAL);
  }

  // Collect system and application metrics
  async collectMetrics() {
    try {
      const [systemMetrics, pm2Metrics, appMetrics] = await Promise.all([
        this.getSystemMetrics(),
        this.getPM2Metrics(),
        this.getApplicationMetrics(),
      ]);

      const timestamp = Date.now();
      const metric = {
        timestamp,
        system: systemMetrics,
        pm2: pm2Metrics,
        app: appMetrics,
      };

      this.metrics.push(metric);
      
      // Keep only recent metrics
      const cutoff = timestamp - CONFIG.METRICS_WINDOW;
      this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
      
      return metric;
    } catch (error) {
      console.error('❌ Error collecting metrics:', error.message);
      return null;
    }
  }

  // Get system metrics (CPU, memory)
  getSystemMetrics() {
    return new Promise((resolve, reject) => {
      exec('top -bn1 | grep "Cpu(s)" | awk \'{print $2}\' | awk -F\'%\' \'{print $1}\'', (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }

        const cpuUsage = parseFloat(stdout.trim());
        
        exec('free | grep Mem | awk \'{printf "%.2f", $3/$2 * 100.0}\'', (error, stdout) => {
          if (error) {
            reject(error);
            return;
          }

          const memoryUsage = parseFloat(stdout.trim());
          
          resolve({
            cpu: cpuUsage,
            memory: memoryUsage,
          });
        });
      });
    });
  }

  // Get PM2 metrics
  getPM2Metrics() {
    return new Promise((resolve, reject) => {
      exec('pm2 jlist', (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }

        try {
          const processes = JSON.parse(stdout);
          const appProcesses = processes.filter(p => p.name === CONFIG.APP_NAME);
          
          const metrics = {
            totalInstances: appProcesses.length,
            runningInstances: appProcesses.filter(p => p.pm2_env.status === 'online').length,
            restarts: appProcesses.reduce((sum, p) => sum + p.pm2_env.restart_time, 0),
            uptime: Math.min(...appProcesses.map(p => p.pm2_env.pm_uptime)),
            memory: appProcesses.reduce((sum, p) => sum + (p.monit?.memory || 0), 0),
            cpu: appProcesses.reduce((sum, p) => sum + (p.monit?.cpu || 0), 0),
          };
          
          resolve(metrics);
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }

  // Get application metrics from health endpoint
  async getApplicationMetrics() {
    try {
      const response = await fetch('http://localhost:3000/api/metrics/realtime');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const data = await response.json();
      return {
        responseTime: data.averageResponseTime || 0,
        requestsPerMinute: data.requestsPerMinute || 0,
        errorRate: data.errorRate || 0,
      };
    } catch (error) {
      console.warn('⚠️  Could not fetch application metrics:', error.message);
      return {
        responseTime: 0,
        requestsPerMinute: 0,
        errorRate: 0,
      };
    }
  }

  // Main monitoring and scaling logic
  async monitorAndScale() {
    if (this.isScaling) {
      console.log('⏳ Scaling operation in progress, skipping check');
      return;
    }

    const currentMetric = await this.collectMetrics();
    if (!currentMetric) {
      return;
    }

    const decision = this.makeScalingDecision(currentMetric);
    
    if (decision.action !== 'none') {
      await this.executeScalingAction(decision);
    }

    this.logMetrics(currentMetric, decision);
  }

  // Analyze metrics and decide on scaling action
  makeScalingDecision(currentMetric) {
    const { system, pm2, app } = currentMetric;
    const now = Date.now();
    
    // Check cooldown period
    const timeSinceLastScale = now - this.lastScaleAction;
    
    // Determine if we should scale up
    const shouldScaleUp = (
      (system.cpu > CONFIG.CPU_SCALE_UP_THRESHOLD ||
       system.memory > CONFIG.MEMORY_SCALE_UP_THRESHOLD ||
       app.responseTime > CONFIG.RESPONSE_TIME_THRESHOLD) &&
      pm2.totalInstances < CONFIG.MAX_INSTANCES &&
      timeSinceLastScale > CONFIG.SCALE_UP_COOLDOWN
    );

    // Determine if we should scale down
    const shouldScaleDown = (
      system.cpu < CONFIG.CPU_SCALE_DOWN_THRESHOLD &&
      system.memory < CONFIG.CPU_SCALE_DOWN_THRESHOLD &&
      app.responseTime < CONFIG.RESPONSE_TIME_THRESHOLD / 2 &&
      pm2.totalInstances > CONFIG.MIN_INSTANCES &&
      timeSinceLastScale > CONFIG.SCALE_DOWN_COOLDOWN
    );

    let action = 'none';
    let reason = 'Metrics within normal range';
    let targetInstances = pm2.totalInstances;

    if (shouldScaleUp) {
      action = 'scale_up';
      targetInstances = Math.min(pm2.totalInstances + 1, CONFIG.MAX_INSTANCES);
      reason = `High resource usage - CPU: ${system.cpu}%, Memory: ${system.memory}%, Response Time: ${app.responseTime}ms`;
    } else if (shouldScaleDown) {
      action = 'scale_down';
      targetInstances = Math.max(pm2.totalInstances - 1, CONFIG.MIN_INSTANCES);
      reason = `Low resource usage - CPU: ${system.cpu}%, Memory: ${system.memory}%`;
    }

    return {
      action,
      reason,
      currentInstances: pm2.totalInstances,
      targetInstances,
      metrics: currentMetric,
    };
  }

  // Execute scaling action
  async executeScalingAction(decision) {
    this.isScaling = true;
    
    try {
      console.log(`🔄 ${decision.action.toUpperCase()}: ${decision.currentInstances} → ${decision.targetInstances} instances`);
      console.log(`📝 Reason: ${decision.reason}`);
      
      const command = `pm2 scale ${CONFIG.APP_NAME} ${decision.targetInstances}`;
      
      await new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            reject(error);
            return;
          }
          
          console.log('✅ Scaling completed successfully');
          console.log(stdout);
          
          resolve();
        });
      });
      
      this.lastScaleAction = Date.now();
      
      // Log scaling action
      this.logScalingAction(decision);
      
    } catch (error) {
      console.error('❌ Scaling failed:', error.message);
    } finally {
      this.isScaling = false;
    }
  }

  // Log current metrics and decisions
  logMetrics(metric, decision) {
    const { system, pm2, app } = metric;
    
    console.log(`📊 Metrics - CPU: ${system.cpu}%, Memory: ${system.memory}%, Instances: ${pm2.totalInstances}, Response Time: ${app.responseTime}ms`);
    
    if (decision.action !== 'none') {
      console.log(`🎯 Decision: ${decision.action} (${decision.reason})`);
    }
  }

  // Log scaling actions to file
  logScalingAction(decision) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      action: decision.action,
      reason: decision.reason,
      currentInstances: decision.currentInstances,
      targetInstances: decision.targetInstances,
      metrics: decision.metrics,
    };

    const logFile = path.join(__dirname, '../logs/scaling.log');
    const logLine = JSON.stringify(logEntry) + '\n';
    
    fs.appendFileSync(logFile, logLine);
  }

  // Get scaling statistics
  getScalingStats() {
    const logFile = path.join(__dirname, '../logs/scaling.log');
    
    if (!fs.existsSync(logFile)) {
      return { totalActions: 0, scaleUps: 0, scaleDowns: 0 };
    }

    const logs = fs.readFileSync(logFile, 'utf8')
      .split('\n')
      .filter(line => line.trim())
      .map(line => JSON.parse(line));

    return {
      totalActions: logs.length,
      scaleUps: logs.filter(log => log.action === 'scale_up').length,
      scaleDowns: logs.filter(log => log.action === 'scale_down').length,
      lastAction: logs[logs.length - 1],
    };
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  const scaler = new AutoScaler();

  switch (command) {
    case 'start':
      scaler.start();
      break;
      
    case 'stats':
      console.log('📈 Scaling Statistics:');
      console.log(JSON.stringify(scaler.getScalingStats(), null, 2));
      break;
      
    case 'test':
      scaler.collectMetrics().then(metrics => {
        console.log('🧪 Test Metrics:');
        console.log(JSON.stringify(metrics, null, 2));
        
        const decision = scaler.makeScalingDecision(metrics);
        console.log('🎯 Scaling Decision:');
        console.log(JSON.stringify(decision, null, 2));
      });
      break;
      
    default:
      console.log('Usage: node auto-scale.js [start|stats|test]');
      console.log('  start - Start auto-scaling monitoring');
      console.log('  stats - Show scaling statistics');
      console.log('  test  - Test metrics collection and decision making');
      process.exit(1);
  }
}

module.exports = AutoScaler;

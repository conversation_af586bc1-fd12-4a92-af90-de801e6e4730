#!/usr/bin/env node

/**
 * Data Migration Script: PostgreSQL to MySQL
 * 
 * This script helps migrate data from PostgreSQL to MySQL format
 * Run this after exporting data from PostgreSQL to CSV files
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

const prisma = new PrismaClient();

// Configuration
const CSV_DIR = './migration-data';
const BATCH_SIZE = 1000;

// Utility function to convert PostgreSQL arrays to JSON
function convertArrayToJson(pgArray) {
  if (!pgArray || pgArray === '{}' || pgArray === '') {
    return null;
  }
  
  try {
    // Remove PostgreSQL array brackets and split
    const cleaned = pgArray.replace(/[{}]/g, '');
    if (!cleaned) return null;
    
    const items = cleaned.split(',').map(item => item.trim().replace(/"/g, ''));
    return items.filter(item => item.length > 0);
  } catch (error) {
    console.warn('Failed to convert array:', pgArray, error.message);
    return null;
  }
}

// Utility function to parse boolean values
function parseBoolean(value) {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true' || value === '1';
  }
  return false;
}

// Utility function to parse dates
function parseDate(dateString) {
  if (!dateString || dateString === '') return null;
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
}

// Migration functions for each table
async function migrateUsers() {
  console.log('Migrating users...');
  const csvPath = path.join(CSV_DIR, 'users.csv');
  
  if (!fs.existsSync(csvPath)) {
    console.log('users.csv not found, skipping...');
    return;
  }

  const users = [];
  let count = 0;

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvPath)
      .pipe(csv())
      .on('data', (row) => {
        users.push({
          id: row.id,
          email: row.email,
          password: row.password,
          name: row.name,
          mobile: row.mobile || null,
          usn: row.usn,
          course: row.course,
          batch: row.batch,
          role: row.role || 'STUDENT',
          status: row.status || 'PENDING',
          profilePicture: row.profilePicture || null,
          bio: row.bio || null,
          linkedinUrl: row.linkedinUrl || null,
          githubUrl: row.githubUrl || null,
          portfolioUrl: row.portfolioUrl || null,
          company: row.company || null,
          jobTitle: row.jobTitle || null,
          experience: row.experience ? parseInt(row.experience) : null,
          location: row.location || null,
          showEmail: parseBoolean(row.showEmail),
          showMobile: parseBoolean(row.showMobile),
          showLinkedin: parseBoolean(row.showLinkedin),
          createdAt: parseDate(row.createdAt) || new Date(),
          updatedAt: parseDate(row.updatedAt) || new Date(),
          lastLoginAt: parseDate(row.lastLoginAt),
        });

        // Process in batches
        if (users.length >= BATCH_SIZE) {
          processBatch();
        }
      })
      .on('end', async () => {
        if (users.length > 0) {
          await processBatch();
        }
        console.log(`Migrated ${count} users`);
        resolve();
      })
      .on('error', reject);

    async function processBatch() {
      try {
        await prisma.user.createMany({
          data: users.splice(0, BATCH_SIZE),
          skipDuplicates: true,
        });
        count += Math.min(BATCH_SIZE, users.length);
        console.log(`Processed ${count} users...`);
      } catch (error) {
        console.error('Error processing user batch:', error.message);
      }
    }
  });
}

async function migrateJobs() {
  console.log('Migrating jobs...');
  const csvPath = path.join(CSV_DIR, 'jobs.csv');
  
  if (!fs.existsSync(csvPath)) {
    console.log('jobs.csv not found, skipping...');
    return;
  }

  const jobs = [];
  let count = 0;

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvPath)
      .pipe(csv())
      .on('data', (row) => {
        jobs.push({
          id: row.id,
          title: row.title,
          company: row.company,
          location: row.location,
          type: row.type || 'FULL_TIME',
          description: row.description,
          requirements: row.requirements || null,
          salary: row.salary || null,
          applicationUrl: row.applicationUrl || null,
          allowResume: parseBoolean(row.allowResume),
          relevantCourses: convertArrayToJson(row.relevantCourses),
          isActive: parseBoolean(row.isActive),
          createdAt: parseDate(row.createdAt) || new Date(),
          updatedAt: parseDate(row.updatedAt) || new Date(),
          expiresAt: parseDate(row.expiresAt),
          postedById: row.postedById,
        });

        if (jobs.length >= BATCH_SIZE) {
          processBatch();
        }
      })
      .on('end', async () => {
        if (jobs.length > 0) {
          await processBatch();
        }
        console.log(`Migrated ${count} jobs`);
        resolve();
      })
      .on('error', reject);

    async function processBatch() {
      try {
        await prisma.job.createMany({
          data: jobs.splice(0, BATCH_SIZE),
          skipDuplicates: true,
        });
        count += Math.min(BATCH_SIZE, jobs.length);
        console.log(`Processed ${count} jobs...`);
      } catch (error) {
        console.error('Error processing job batch:', error.message);
      }
    }
  });
}

async function migrateEvents() {
  console.log('Migrating events...');
  const csvPath = path.join(CSV_DIR, 'events.csv');
  
  if (!fs.existsSync(csvPath)) {
    console.log('events.csv not found, skipping...');
    return;
  }

  const events = [];
  let count = 0;

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvPath)
      .pipe(csv())
      .on('data', (row) => {
        events.push({
          id: row.id,
          title: row.title,
          description: row.description,
          location: row.location || null,
          imageUrl: row.imageUrl || null,
          startTime: parseDate(row.startTime) || new Date(),
          endTime: parseDate(row.endTime),
          isOnline: parseBoolean(row.isOnline),
          meetingUrl: row.meetingUrl || null,
          maxAttendees: row.maxAttendees ? parseInt(row.maxAttendees) : null,
          isActive: parseBoolean(row.isActive),
          createdAt: parseDate(row.createdAt) || new Date(),
          updatedAt: parseDate(row.updatedAt) || new Date(),
          organizerId: row.organizerId,
        });

        if (events.length >= BATCH_SIZE) {
          processBatch();
        }
      })
      .on('end', async () => {
        if (events.length > 0) {
          await processBatch();
        }
        console.log(`Migrated ${count} events`);
        resolve();
      })
      .on('error', reject);

    async function processBatch() {
      try {
        await prisma.event.createMany({
          data: events.splice(0, BATCH_SIZE),
          skipDuplicates: true,
        });
        count += Math.min(BATCH_SIZE, events.length);
        console.log(`Processed ${count} events...`);
      } catch (error) {
        console.error('Error processing event batch:', error.message);
      }
    }
  });
}

// Add more migration functions for other tables...

async function main() {
  try {
    console.log('Starting data migration from PostgreSQL to MySQL...');
    console.log(`Looking for CSV files in: ${CSV_DIR}`);

    // Check if migration data directory exists
    if (!fs.existsSync(CSV_DIR)) {
      console.error(`Migration data directory not found: ${CSV_DIR}`);
      console.log('Please create the directory and place your CSV files there.');
      process.exit(1);
    }

    // Run migrations in order (respecting foreign key constraints)
    await migrateUsers();
    await migrateJobs();
    await migrateEvents();
    
    // Add more migrations as needed:
    // await migrateJobApplications();
    // await migrateEventRSVPs();
    // await migratePosts();
    // await migrateMessages();
    // await migrateConnections();
    // await migrateNotifications();
    // await migrateNotificationPreferences();

    console.log('✅ Migration completed successfully!');
    console.log('Please verify your data using Prisma Studio: npx prisma studio');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Handle script execution
if (require.main === module) {
  main();
}

module.exports = {
  migrateUsers,
  migrateJobs,
  migrateEvents,
  convertArrayToJson,
  parseBoolean,
  parseDate,
};

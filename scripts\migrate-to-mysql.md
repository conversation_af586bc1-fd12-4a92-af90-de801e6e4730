# Migration Guide: PostgreSQL to MySQL

This guide helps you migrate your existing Alumni Portal data from PostgreSQL to MySQL.

## Prerequisites

- MySQL 8.0+ installed and running
- Access to your existing PostgreSQL database
- Node.js and npm installed
- Backup of your PostgreSQL data

## Step 1: Backup PostgreSQL Data

```bash
# Create a backup of your PostgreSQL database
pg_dump -h localhost -U postgres -d alumni_portal > alumni_portal_backup.sql

# Or with custom format for better compression
pg_dump -h localhost -U postgres -d alumni_portal -Fc > alumni_portal_backup.dump
```

## Step 2: Export Data to CSV

Since direct migration between PostgreSQL and MySQL can be complex, we'll export data to CSV format:

```sql
-- Connect to PostgreSQL
psql -h localhost -U postgres -d alumni_portal

-- Export users table
\copy users TO 'users.csv' WITH CSV HEADER;

-- Export jobs table
\copy jobs TO 'jobs.csv' WITH CSV HEADER;

-- Export events table
\copy events TO 'events.csv' WITH CSV HEADER;

-- Export messages table
\copy messages TO 'messages.csv' WITH CSV HEADER;

-- Export notifications table
\copy notifications TO 'notifications.csv' WITH CSV HEADER;

-- Export other tables as needed
\copy job_applications TO 'job_applications.csv' WITH CSV HEADER;
\copy event_rsvps TO 'event_rsvps.csv' WITH CSV HEADER;
\copy posts TO 'posts.csv' WITH CSV HEADER;
\copy connections TO 'connections.csv' WITH CSV HEADER;
\copy notification_preferences TO 'notification_preferences.csv' WITH CSV HEADER;
```

## Step 3: Update Configuration

1. **Update environment variables:**
```env
# Change from PostgreSQL to MySQL
DATABASE_URL="mysql://username:password@localhost:3306/alumni_portal"
```

2. **Install MySQL driver:**
```bash
npm install mysql2
```

3. **Update Prisma schema** (already done in this migration)

## Step 4: Create MySQL Database

```sql
-- Connect to MySQL
mysql -u root -p

-- Create database
CREATE DATABASE alumni_portal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional)
CREATE USER 'alumni_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON alumni_portal.* TO 'alumni_user'@'localhost';
FLUSH PRIVILEGES;
```

## Step 5: Run Prisma Migration

```bash
# Generate new Prisma client for MySQL
npx prisma generate

# Create and run migration
npx prisma migrate dev --name init

# Or for production
npx prisma migrate deploy
```

## Step 6: Import Data to MySQL

### Option A: Using MySQL LOAD DATA (Recommended)

```sql
-- Connect to MySQL
mysql -u root -p alumni_portal

-- Import users
LOAD DATA LOCAL INFILE 'users.csv'
INTO TABLE users
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Import jobs (handle JSON field for relevantCourses)
LOAD DATA LOCAL INFILE 'jobs.csv'
INTO TABLE jobs
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(id, title, company, location, type, description, requirements, salary, applicationUrl, allowResume, @relevant_courses, isActive, createdAt, updatedAt, expiresAt, postedById)
SET relevantCourses = CASE 
    WHEN @relevant_courses = '' OR @relevant_courses IS NULL THEN NULL
    ELSE JSON_ARRAY(@relevant_courses)
END;

-- Continue with other tables...
```

### Option B: Using Node.js Script

Create a migration script `scripts/migrate-data.js`:

```javascript
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const csv = require('csv-parser');

const prisma = new PrismaClient();

async function migrateUsers() {
  const users = [];
  
  return new Promise((resolve, reject) => {
    fs.createReadStream('users.csv')
      .pipe(csv())
      .on('data', (row) => {
        // Transform PostgreSQL data to MySQL format
        users.push({
          id: row.id,
          email: row.email,
          password: row.password,
          name: row.name,
          mobile: row.mobile || null,
          usn: row.usn,
          course: row.course,
          batch: row.batch,
          role: row.role,
          status: row.status,
          // ... other fields
          createdAt: new Date(row.createdAt),
          updatedAt: new Date(row.updatedAt),
        });
      })
      .on('end', async () => {
        try {
          await prisma.user.createMany({
            data: users,
            skipDuplicates: true,
          });
          console.log(`Migrated ${users.length} users`);
          resolve();
        } catch (error) {
          reject(error);
        }
      });
  });
}

async function migrateJobs() {
  const jobs = [];
  
  return new Promise((resolve, reject) => {
    fs.createReadStream('jobs.csv')
      .pipe(csv())
      .on('data', (row) => {
        jobs.push({
          id: row.id,
          title: row.title,
          company: row.company,
          location: row.location,
          type: row.type,
          description: row.description,
          requirements: row.requirements || null,
          salary: row.salary || null,
          applicationUrl: row.applicationUrl || null,
          allowResume: row.allowResume === 'true',
          // Convert PostgreSQL array to JSON
          relevantCourses: row.relevantCourses ? 
            JSON.parse(row.relevantCourses.replace(/[{}]/g, '').split(',').map(s => s.trim())) : 
            null,
          isActive: row.isActive === 'true',
          createdAt: new Date(row.createdAt),
          updatedAt: new Date(row.updatedAt),
          expiresAt: row.expiresAt ? new Date(row.expiresAt) : null,
          postedById: row.postedById,
        });
      })
      .on('end', async () => {
        try {
          await prisma.job.createMany({
            data: jobs,
            skipDuplicates: true,
          });
          console.log(`Migrated ${jobs.length} jobs`);
          resolve();
        } catch (error) {
          reject(error);
        }
      });
  });
}

async function main() {
  try {
    console.log('Starting migration...');
    
    await migrateUsers();
    await migrateJobs();
    // Add other migration functions...
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
```

## Step 7: Verify Migration

```bash
# Check data integrity
npx prisma studio

# Run application tests
npm test

# Check application functionality
npm run dev
```

## Step 8: Update Production Environment

1. **Schedule maintenance window**
2. **Stop application services**
3. **Run final data sync**
4. **Update environment variables**
5. **Deploy updated application**
6. **Verify functionality**
7. **Resume services**

## Troubleshooting

### Common Issues

1. **Character encoding issues:**
   - Ensure MySQL database uses `utf8mb4` charset
   - Check CSV file encoding

2. **Date format issues:**
   - PostgreSQL and MySQL may handle timestamps differently
   - Convert dates during import

3. **JSON field issues:**
   - PostgreSQL arrays need to be converted to JSON format
   - Handle null values properly

4. **Foreign key constraints:**
   - Import data in correct order (parent tables first)
   - Temporarily disable foreign key checks if needed

### Rollback Plan

If migration fails:

1. **Stop MySQL application**
2. **Restore PostgreSQL configuration**
3. **Restart with PostgreSQL**
4. **Investigate and fix issues**
5. **Retry migration**

## Performance Considerations

- **Batch imports:** Use batch inserts for large datasets
- **Indexes:** Create indexes after data import for better performance
- **Connection pooling:** Adjust connection pool settings for MySQL
- **Query optimization:** Review and optimize queries for MySQL

## Post-Migration Tasks

1. **Update monitoring dashboards**
2. **Update backup scripts**
3. **Update documentation**
4. **Train team on MySQL-specific tools**
5. **Monitor performance metrics**

This migration guide ensures a smooth transition from PostgreSQL to MySQL while preserving all your data and functionality.

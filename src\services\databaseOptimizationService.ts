import { prisma } from "@/config/database";
import { Logger } from "@/services/loggerService";

// Query optimization configuration
const OPTIMIZATION_CONFIG = {
  // Enable query logging for slow queries
  enableSlowQueryLogging: process.env.ENABLE_SLOW_QUERY_LOGGING !== "false",
  
  // Slow query threshold in milliseconds
  slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD || "1000"),
  
  // Enable query analysis
  enableQueryAnalysis: process.env.ENABLE_QUERY_ANALYSIS !== "false",
  
  // Maximum query result size before warning
  maxResultSize: parseInt(process.env.MAX_QUERY_RESULT_SIZE || "10000"),
};

// Query performance metrics
interface QueryMetrics {
  query: string;
  duration: number;
  resultCount: number;
  timestamp: number;
  model?: string;
  operation?: string;
}

export class DatabaseOptimizationService {
  private static queryMetrics: QueryMetrics[] = [];
  private static readonly MAX_METRICS = 1000;

  // Initialize database optimization service
  static init() {
    if (OPTIMIZATION_CONFIG.enableQueryAnalysis) {
      this.setupQueryMonitoring();
      Logger.info("Database optimization service initialized");
    }
  }

  // Setup query monitoring using Prisma middleware
  private static setupQueryMonitoring() {
    // Note: This is a conceptual implementation
    // In practice, you'd need to use Prisma's logging or middleware features
    Logger.info("Query monitoring setup completed");
  }

  // Analyze query performance
  static analyzeQuery(query: string, duration: number, resultCount: number, model?: string, operation?: string) {
    const metric: QueryMetrics = {
      query,
      duration,
      resultCount,
      timestamp: Date.now(),
      model,
      operation,
    };

    // Add to metrics
    this.queryMetrics.push(metric);
    if (this.queryMetrics.length > this.MAX_METRICS) {
      this.queryMetrics = this.queryMetrics.slice(-this.MAX_METRICS);
    }

    // Log slow queries
    if (OPTIMIZATION_CONFIG.enableSlowQueryLogging && duration > OPTIMIZATION_CONFIG.slowQueryThreshold) {
      Logger.warn("Slow query detected", {
        query: query.substring(0, 200) + (query.length > 200 ? "..." : ""),
        duration: `${duration}ms`,
        resultCount,
        model,
        operation,
      });
    }

    // Warn about large result sets
    if (resultCount > OPTIMIZATION_CONFIG.maxResultSize) {
      Logger.warn("Large result set detected", {
        query: query.substring(0, 200) + (query.length > 200 ? "..." : ""),
        resultCount,
        model,
        operation,
      });
    }
  }

  // Get query performance statistics
  static getQueryStats() {
    if (this.queryMetrics.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        largeResultSets: 0,
        topSlowQueries: [],
        queryDistribution: {},
      };
    }

    const totalQueries = this.queryMetrics.length;
    const totalDuration = this.queryMetrics.reduce((sum, metric) => sum + metric.duration, 0);
    const averageDuration = totalDuration / totalQueries;
    
    const slowQueries = this.queryMetrics.filter(m => m.duration > OPTIMIZATION_CONFIG.slowQueryThreshold).length;
    const largeResultSets = this.queryMetrics.filter(m => m.resultCount > OPTIMIZATION_CONFIG.maxResultSize).length;

    // Top 10 slowest queries
    const topSlowQueries = [...this.queryMetrics]
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10)
      .map(metric => ({
        query: metric.query.substring(0, 100) + (metric.query.length > 100 ? "..." : ""),
        duration: metric.duration,
        resultCount: metric.resultCount,
        model: metric.model,
        operation: metric.operation,
      }));

    // Query distribution by model/operation
    const queryDistribution = this.queryMetrics.reduce((dist, metric) => {
      const key = `${metric.model || 'unknown'}.${metric.operation || 'unknown'}`;
      if (!dist[key]) {
        dist[key] = { count: 0, totalDuration: 0, averageDuration: 0 };
      }
      dist[key].count++;
      dist[key].totalDuration += metric.duration;
      dist[key].averageDuration = dist[key].totalDuration / dist[key].count;
      return dist;
    }, {} as Record<string, { count: number; totalDuration: number; averageDuration: number }>);

    return {
      totalQueries,
      averageDuration: Math.round(averageDuration * 100) / 100,
      slowQueries,
      largeResultSets,
      topSlowQueries,
      queryDistribution,
      timestamp: new Date().toISOString(),
    };
  }

  // Optimize pagination queries
  static optimizePagination(page: number, limit: number, maxLimit: number = 100) {
    const optimizedLimit = Math.min(limit, maxLimit);
    const offset = (page - 1) * optimizedLimit;
    
    return {
      take: optimizedLimit,
      skip: offset,
      page,
      limit: optimizedLimit,
    };
  }

  // Create optimized where clauses
  static createOptimizedWhere(filters: Record<string, any>) {
    const where: any = {};
    
    for (const [key, value] of Object.entries(filters)) {
      if (value === undefined || value === null || value === '') {
        continue;
      }

      // Handle different filter types
      if (typeof value === 'string') {
        // Use case-insensitive search for strings
        where[key] = {
          contains: value,
          mode: 'insensitive',
        };
      } else if (Array.isArray(value)) {
        // Use 'in' for arrays
        where[key] = {
          in: value,
        };
      } else if (typeof value === 'object' && value.from && value.to) {
        // Handle date ranges
        where[key] = {
          gte: new Date(value.from),
          lte: new Date(value.to),
        };
      } else {
        // Direct equality for other types
        where[key] = value;
      }
    }

    return where;
  }

  // Suggest database indexes based on query patterns
  static suggestIndexes() {
    const suggestions: Array<{
      table: string;
      columns: string[];
      reason: string;
      priority: 'high' | 'medium' | 'low';
    }> = [];

    // Analyze query patterns to suggest indexes
    const queryPatterns = this.analyzeQueryPatterns();

    // Common index suggestions based on typical usage patterns
    suggestions.push(
      {
        table: 'User',
        columns: ['email'],
        reason: 'Frequently used for authentication and user lookup',
        priority: 'high',
      },
      {
        table: 'User',
        columns: ['status', 'role'],
        reason: 'Often filtered by status and role in user queries',
        priority: 'medium',
      },
      {
        table: 'Job',
        columns: ['status', 'createdAt'],
        reason: 'Job listings are frequently filtered by status and sorted by creation date',
        priority: 'high',
      },
      {
        table: 'Job',
        columns: ['companyName'],
        reason: 'Company-based job searches are common',
        priority: 'medium',
      },
      {
        table: 'Event',
        columns: ['eventDate', 'status'],
        reason: 'Events are typically queried by date and status',
        priority: 'high',
      },
      {
        table: 'Message',
        columns: ['senderId', 'receiverId', 'createdAt'],
        reason: 'Message queries typically involve sender/receiver and are sorted by date',
        priority: 'high',
      },
      {
        table: 'Notification',
        columns: ['userId', 'isRead', 'createdAt'],
        reason: 'Notifications are queried by user, read status, and sorted by date',
        priority: 'high',
      }
    );

    return suggestions;
  }

  // Analyze query patterns from metrics
  private static analyzeQueryPatterns() {
    const patterns = {
      frequentFilters: new Map<string, number>(),
      frequentSorts: new Map<string, number>(),
      frequentJoins: new Map<string, number>(),
    };

    // This would analyze actual query patterns from the metrics
    // For now, return empty patterns
    return patterns;
  }

  // Generate database health report
  static async generateHealthReport() {
    try {
      const queryStats = this.getQueryStats();
      const indexSuggestions = this.suggestIndexes();

      // Get database connection info (if available)
      const connectionInfo = await this.getConnectionInfo();

      return {
        timestamp: new Date().toISOString(),
        performance: queryStats,
        indexSuggestions,
        connection: connectionInfo,
        recommendations: this.generateRecommendations(queryStats),
      };
    } catch (error) {
      Logger.error("Failed to generate database health report", error);
      throw error;
    }
  }

  // Get database connection information
  private static async getConnectionInfo() {
    try {
      // This would get actual connection pool information
      // For now, return basic info
      return {
        status: "connected",
        poolSize: "managed by Prisma",
        activeConnections: "unknown",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: "error",
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Generate performance recommendations
  private static generateRecommendations(queryStats: any) {
    const recommendations: Array<{
      type: 'performance' | 'indexing' | 'query' | 'configuration';
      priority: 'high' | 'medium' | 'low';
      title: string;
      description: string;
      action?: string;
    }> = [];

    // Slow query recommendations
    if (queryStats.slowQueries > queryStats.totalQueries * 0.1) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        title: 'High number of slow queries detected',
        description: `${queryStats.slowQueries} out of ${queryStats.totalQueries} queries are slower than ${OPTIMIZATION_CONFIG.slowQueryThreshold}ms`,
        action: 'Review and optimize slow queries, consider adding indexes',
      });
    }

    // Large result set recommendations
    if (queryStats.largeResultSets > 0) {
      recommendations.push({
        type: 'query',
        priority: 'medium',
        title: 'Large result sets detected',
        description: `${queryStats.largeResultSets} queries returned more than ${OPTIMIZATION_CONFIG.maxResultSize} results`,
        action: 'Implement pagination or add more specific filters',
      });
    }

    // Average query time recommendations
    if (queryStats.averageDuration > 500) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        title: 'High average query duration',
        description: `Average query duration is ${queryStats.averageDuration}ms`,
        action: 'Review query patterns and consider database optimization',
      });
    }

    return recommendations;
  }

  // Clear query metrics
  static clearMetrics() {
    this.queryMetrics = [];
    Logger.info("Database query metrics cleared");
  }

  // Export query metrics for analysis
  static exportMetrics() {
    return {
      metrics: this.queryMetrics,
      config: OPTIMIZATION_CONFIG,
      exportedAt: new Date().toISOString(),
    };
  }
}

// Initialize the service
DatabaseOptimizationService.init();

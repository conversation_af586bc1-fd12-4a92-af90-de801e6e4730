import { prisma } from "@/config/database";
import { DatabaseOptimizationService } from "@/services/databaseOptimizationService";
import { Logger } from "@/services/loggerService";

// Enhanced query wrapper with performance monitoring
export class DatabaseQuery {
  // Execute a query with performance monitoring
  static async execute<T>(
    operation: string,
    model: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      
      // Determine result count
      let resultCount = 0;
      if (Array.isArray(result)) {
        resultCount = result.length;
      } else if (result && typeof result === 'object') {
        resultCount = 1;
      }

      // Analyze query performance
      DatabaseOptimizationService.analyzeQuery(
        `${model}.${operation}`,
        duration,
        resultCount,
        model,
        operation
      );

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      Logger.error(`Database query failed: ${model}.${operation}`, {
        error: error instanceof Error ? error.message : String(error),
        duration: `${duration}ms`,
      });
      
      throw error;
    }
  }

  // Optimized findMany with pagination
  static async findMany<T>(
    model: any,
    options: {
      where?: any;
      orderBy?: any;
      include?: any;
      select?: any;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ data: T[]; total: number; pagination: any }> {
    const { page = 1, limit = 20, where, orderBy, include, select } = options;
    
    // Optimize pagination
    const pagination = DatabaseOptimizationService.optimizePagination(page, limit);
    
    return this.execute('findMany', model.name || 'unknown', async () => {
      const [data, total] = await Promise.all([
        model.findMany({
          where,
          orderBy,
          include,
          select,
          take: pagination.take,
          skip: pagination.skip,
        }),
        model.count({ where }),
      ]);

      return {
        data,
        total,
        pagination: {
          ...pagination,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.skip + pagination.take < total,
          hasPrev: page > 1,
        },
      };
    });
  }

  // Optimized search with filters
  static async search<T>(
    model: any,
    searchOptions: {
      query?: string;
      filters?: Record<string, any>;
      searchFields?: string[];
      page?: number;
      limit?: number;
      orderBy?: any;
      include?: any;
      select?: any;
    }
  ): Promise<{ data: T[]; total: number; pagination: any }> {
    const {
      query,
      filters = {},
      searchFields = [],
      page = 1,
      limit = 20,
      orderBy,
      include,
      select,
    } = searchOptions;

    // Build optimized where clause
    let where = DatabaseOptimizationService.createOptimizedWhere(filters);

    // Add search functionality
    if (query && searchFields.length > 0) {
      const searchConditions = searchFields.map(field => ({
        [field]: {
          contains: query,
          mode: 'insensitive' as const,
        },
      }));

      where = {
        ...where,
        OR: searchConditions,
      };
    }

    return this.findMany(model, {
      where,
      orderBy,
      include,
      select,
      page,
      limit,
    });
  }

  // Batch operations with performance monitoring
  static async batchCreate<T>(
    model: any,
    data: any[],
    batchSize: number = 100
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      const batchResult = await this.execute(
        'createMany',
        model.name || 'unknown',
        async () => {
          return model.createMany({
            data: batch,
            skipDuplicates: true,
          });
        }
      );
      
      results.push(batchResult);
    }

    return results;
  }

  // Optimized aggregation queries
  static async aggregate<T>(
    model: any,
    options: {
      where?: any;
      _count?: any;
      _sum?: any;
      _avg?: any;
      _min?: any;
      _max?: any;
      groupBy?: any;
    }
  ): Promise<T> {
    return this.execute('aggregate', model.name || 'unknown', async () => {
      return model.aggregate(options);
    });
  }

  // Transaction wrapper with monitoring
  static async transaction<T>(
    operations: ((tx: any) => Promise<any>)[]
  ): Promise<T[]> {
    return this.execute('transaction', 'prisma', async () => {
      return prisma.$transaction(operations);
    });
  }

  // Raw query execution with monitoring
  static async rawQuery<T>(
    query: string,
    params: any[] = []
  ): Promise<T> {
    return this.execute('rawQuery', 'prisma', async () => {
      return prisma.$queryRawUnsafe(query, ...params);
    });
  }

  // Optimized exists check
  static async exists(
    model: any,
    where: any
  ): Promise<boolean> {
    return this.execute('exists', model.name || 'unknown', async () => {
      const result = await model.findFirst({
        where,
        select: { id: true },
      });
      return !!result;
    });
  }

  // Soft delete with optimized queries
  static async softDelete(
    model: any,
    where: any,
    deletedField: string = 'deletedAt'
  ): Promise<any> {
    return this.execute('softDelete', model.name || 'unknown', async () => {
      return model.updateMany({
        where: {
          ...where,
          [deletedField]: null,
        },
        data: {
          [deletedField]: new Date(),
        },
      });
    });
  }

  // Bulk update with batching
  static async bulkUpdate(
    model: any,
    updates: Array<{ where: any; data: any }>,
    batchSize: number = 50
  ): Promise<any[]> {
    const results = [];
    
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      const batchResults = await this.transaction(
        batch.map(({ where, data }) => (tx: any) => tx[model.name].update({ where, data }))
      );
      
      results.push(...batchResults);
    }

    return results;
  }

  // Get related data with optimized includes
  static async getWithRelations<T>(
    model: any,
    id: string,
    relations: Record<string, any> = {}
  ): Promise<T | null> {
    return this.execute('findUnique', model.name || 'unknown', async () => {
      return model.findUnique({
        where: { id },
        include: relations,
      });
    });
  }

  // Count with filters
  static async countWithFilters(
    model: any,
    filters: Record<string, any> = {}
  ): Promise<number> {
    const where = DatabaseOptimizationService.createOptimizedWhere(filters);
    
    return this.execute('count', model.name || 'unknown', async () => {
      return model.count({ where });
    });
  }

  // Get distinct values
  static async getDistinct<T>(
    model: any,
    field: string,
    where: any = {}
  ): Promise<T[]> {
    return this.execute('findMany', model.name || 'unknown', async () => {
      return model.findMany({
        where,
        select: { [field]: true },
        distinct: [field],
      });
    });
  }

  // Optimized upsert operation
  static async upsert<T>(
    model: any,
    where: any,
    create: any,
    update: any
  ): Promise<T> {
    return this.execute('upsert', model.name || 'unknown', async () => {
      return model.upsert({
        where,
        create,
        update,
      });
    });
  }
}

// Helper functions for common query patterns
export const QueryHelpers = {
  // Build date range filter
  dateRange: (field: string, from?: string, to?: string) => {
    const filter: any = {};
    if (from) filter.gte = new Date(from);
    if (to) filter.lte = new Date(to);
    return Object.keys(filter).length > 0 ? { [field]: filter } : {};
  },

  // Build text search filter
  textSearch: (fields: string[], query: string) => {
    if (!query) return {};
    return {
      OR: fields.map(field => ({
        [field]: {
          contains: query,
          mode: 'insensitive' as const,
        },
      })),
    };
  },

  // Build status filter
  statusFilter: (status?: string | string[]) => {
    if (!status) return {};
    if (Array.isArray(status)) {
      return { status: { in: status } };
    }
    return { status };
  },

  // Build user filter
  userFilter: (userId?: string) => {
    if (!userId) return {};
    return { userId };
  },

  // Build pagination info
  paginationInfo: (page: number, limit: number, total: number) => {
    const totalPages = Math.ceil(total / limit);
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      prevPage: page > 1 ? page - 1 : null,
    };
  },
};
